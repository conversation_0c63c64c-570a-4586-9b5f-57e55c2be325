<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>Electron</title>
    <!-- https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP -->
    <meta
      http-equiv="Content-Security-Policy"
      content="default-src * 'unsafe-inline' 'unsafe-eval' data: blob: filesystem:;
               script-src * 'unsafe-inline' 'unsafe-eval' blob:;
               style-src * 'unsafe-inline';
               img-src * data: blob: filesystem:;
               connect-src *;
               frame-src *;
               worker-src * blob:;
               child-src * blob:;
               object-src 'none';"
    />
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
