import React from 'react'

function PageLayout({ 
  title, 
  onBack, 
  children,
  backText = "返回主页",
  showBottomBar = true 
}) {
  return (
    <div className="fixed inset-0 z-50 flex flex-col bg-gradient-to-b from-emerald-600 to-emerald-800">
      {/* 顶部区域 */}
      <div className="bg-gradient-to-r from-emerald-700 to-emerald-600 shadow-lg">
        {/* 标题栏 */}
        <div className="h-16 flex items-center justify-between px-6">
          <button
            onClick={onBack}
            className="text-white flex items-center gap-2 hover:bg-emerald-600/50 px-4 py-2 rounded-lg transition-all duration-300"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            {backText}
          </button>
          <div className="text-white text-2xl font-bold tracking-wider">
            {title}
          </div>
          <div className="w-24"></div>
        </div>

        {/* 装饰条 */}
        <div className="h-2 bg-gradient-to-r from-emerald-500/20 via-white/20 to-emerald-500/20"></div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 bg-white overflow-hidden">
        {children}
      </div>

      {/* 底部区域 */}
      {showBottomBar && (
        <div className="bg-gradient-to-r from-emerald-700 to-emerald-600 shadow-lg">
          {/* 装饰条 */}
          <div className="h-2 bg-gradient-to-r from-emerald-500/20 via-white/20 to-emerald-500/20"></div>
          
          {/* 底部工具栏 */}
          <div className="h-16 flex items-center justify-between px-6">
            <button
              onClick={onBack}
              className="text-white flex items-center gap-2 hover:bg-emerald-600/50 px-6 py-3 rounded-lg
                        transition-all duration-300 border border-white/20"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              {backText}
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default PageLayout 