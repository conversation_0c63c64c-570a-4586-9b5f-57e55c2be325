{"name": "yancao", "version": "1.0.0", "description": "An Electron application with React", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "axios": "^1.7.7", "browser-image-compression": "^2.0.2", "electron-updater": "^6.1.7", "moment": "^2.30.1", "node-schedule": "^2.1.1", "react-icons": "^5.5.0", "react-image-crop": "^11.0.7", "react-pdf": "^9.1.1", "react-router-dom": "^6.27.0"}, "devDependencies": {"@electron-toolkit/eslint-config": "^1.0.2", "@electron-toolkit/eslint-config-prettier": "^2.0.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "electron": "^31.0.2", "electron-builder": "^24.13.3", "electron-vite": "^2.3.0", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.3", "postcss": "^8.4.47", "prettier": "^3.3.2", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwindcss": "^3.4.14", "vite": "^5.3.1"}}