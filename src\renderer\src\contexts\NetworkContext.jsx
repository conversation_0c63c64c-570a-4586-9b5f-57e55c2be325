import React, { createContext, useContext, useState, useEffect } from 'react'

const NetworkContext = createContext()

/**
 * 获取网络状态上下文
 * @returns {Object} 网络状态上下文对象
 */
export const useNetwork = () => {
  const context = useContext(NetworkContext)
  if (!context) {
    throw new Error('useNetwork must be used within a NetworkProvider')
  }
  return context
}

/**
 * 网络状态提供者组件
 * 管理全局网络状态并提供相关方法
 */
export const NetworkProvider = ({ children }) => {
  const [isOnline, setIsOnline] = useState(true)
  const [isChecking, setIsChecking] = useState(false)

  useEffect(() => {
    // 获取初始网络状态
    const getInitialStatus = async () => {
      try {
        const status = await window.api.getNetworkStatus()
        setIsOnline(status)
      } catch (error) {
        console.error('获取网络状态失败:', error)
        setIsOnline(false)
      }
    }

    getInitialStatus()

    // 监听网络状态变化
    const handleNetworkChange = (online) => {
      setIsOnline(online)
    }

    window.api.onNetworkStatusChanged(handleNetworkChange)

    // 清理监听器
    return () => {
      window.api.removeNetworkStatusListener()
    }
  }, [])

  /**
   * 手动检查网络状态
   * @returns {Promise<boolean>} 网络连接状态
   */
  const checkNetworkStatus = async () => {
    setIsChecking(true)
    try {
      const status = await window.api.checkNetworkStatus()
      setIsOnline(status)
      return status
    } catch (error) {
      console.error('检查网络状态失败:', error)
      setIsOnline(false)
      return false
    } finally {
      setIsChecking(false)
    }
  }

  /**
   * 检查网络连接并执行回调
   * @param {Function} onSuccess - 网络连接成功时的回调
   * @param {Function} onError - 网络连接失败时的回调
   */
  const withNetworkCheck = async (onSuccess, onError) => {
    if (isOnline) {
      if (onSuccess) onSuccess()
    } else {
      // 先检查一次网络状态
      const status = await checkNetworkStatus()
      if (status) {
        if (onSuccess) onSuccess()
      } else {
        if (onError) onError()
      }
    }
  }

  const value = {
    isOnline,
    isChecking,
    checkNetworkStatus,
    withNetworkCheck
  }

  return <NetworkContext.Provider value={value}>{children}</NetworkContext.Provider>
}
