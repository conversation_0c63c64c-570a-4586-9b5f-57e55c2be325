# Camera自动跳转功能测试计划

## 测试目标
验证Camera组件的自动跳转功能是否按照需求正确实现。

## 测试环境
- 操作系统: Windows
- 浏览器: Electron应用
- 摄像头: 需要可用的摄像头设备

## 测试用例

### 1. 基本自动跳转流程测试

#### 测试用例1.1: 文档模式自动跳转
**步骤:**
1. 启动应用，打开Camera组件
2. 确认当前为文档模式
3. 拍摄一张照片并保存
4. 观察是否显示3秒倒计时
5. 等待倒计时结束

**预期结果:**
- 保存成功后显示"照片保存成功"提示
- 显示3秒倒计时界面，提示"即将自动切换到身份证模式"
- 倒计时结束后自动切换到身份证模式
- 显示"已切换到身份证模式"提示

#### 测试用例1.2: 身份证模式自动跳转
**步骤:**
1. 在身份证模式下拍摄正面照片并保存
2. 观察是否自动切换到反面
3. 拍摄反面照片并保存
4. 观察是否显示倒计时并跳转到人像模式

**预期结果:**
- 正面拍摄完成后显示"身份证正面拍照完成，即将切换到反面"
- 1.5秒后自动切换到反面，显示"请拍摄身份证反面"
- 反面拍摄完成后显示"身份证反面拍照完成"
- 显示3秒倒计时，提示"即将自动切换到人像模式"
- 倒计时结束后自动切换到人像模式

#### 测试用例1.3: 人像模式完成测试
**步骤:**
1. 在人像模式下拍摄照片并保存
2. 观察最终提示信息

**预期结果:**
- 显示"照片保存成功"提示
- 显示"所有拍摄模式已完成！"提示
- 不会尝试跳转到其他模式

### 2. 手动取消自动跳转测试

#### 测试用例2.1: 倒计时期间手动切换
**步骤:**
1. 在文档模式拍摄并保存照片
2. 等待倒计时开始显示
3. 在倒计时期间点击"人像模式"按钮
4. 观察倒计时是否取消

**预期结果:**
- 倒计时界面立即消失
- 直接切换到人像模式
- 不会自动跳转到身份证模式

#### 测试用例2.2: 身份证模式手动切换
**步骤:**
1. 在身份证正面拍摄完成后
2. 在自动切换到反面之前手动点击"文档模式"
3. 观察是否正确切换

**预期结果:**
- 取消自动切换到反面的操作
- 直接切换到文档模式
- 身份证模式重置为正面状态

### 3. 界面显示测试

#### 测试用例3.1: 倒计时界面显示
**步骤:**
1. 触发自动跳转倒计时
2. 检查倒计时界面的显示内容

**预期结果:**
- 显示大号倒计时数字（3、2、1）
- 显示"即将自动切换到XX模式"文字
- 显示"您可以手动切换模式来取消自动跳转"提示
- 界面居中显示，有适当的背景遮罩

#### 测试用例3.2: 拍摄进度指示器
**步骤:**
1. 按顺序完成各模式的拍摄
2. 观察左侧进度指示器的状态变化

**预期结果:**
- 完成的模式显示绿色勾选标记
- 未完成的模式显示灰色圆圈
- 身份证正反面分别显示状态
- 所有完成后显示"许可申请"按钮

### 4. 边界情况测试

#### 测试用例4.1: 快速连续操作
**步骤:**
1. 快速连续拍摄多张照片
2. 在倒计时期间快速切换模式
3. 观察系统响应

**预期结果:**
- 系统能正确处理快速操作
- 不会出现状态混乱
- 倒计时能正确取消

#### 测试用例4.2: 摄像头权限测试
**步骤:**
1. 在没有摄像头权限的情况下测试
2. 观察自动跳转是否仍然工作

**预期结果:**
- 显示摄像头权限错误
- 自动跳转逻辑不受影响
- 用户仍可手动切换模式

### 5. 性能测试

#### 测试用例5.1: 内存泄漏测试
**步骤:**
1. 重复执行完整的拍摄流程多次
2. 监控内存使用情况
3. 检查是否有定时器泄漏

**预期结果:**
- 内存使用保持稳定
- 没有未清理的定时器
- 应用响应保持流畅

## 测试检查清单

- [ ] 文档模式自动跳转到身份证模式
- [ ] 身份证正面自动切换到反面
- [ ] 身份证反面自动跳转到人像模式
- [ ] 人像模式完成后显示完成提示
- [ ] 倒计时界面正确显示
- [ ] 手动切换能取消自动跳转
- [ ] 拍摄进度指示器状态正确
- [ ] 所有提示信息显示正确
- [ ] 边界情况处理正确
- [ ] 性能表现良好

## 测试报告模板

**测试日期:** ___________
**测试人员:** ___________
**测试环境:** ___________

**测试结果:**
- 通过用例数: ___/___
- 失败用例数: ___
- 发现问题: ___________

**问题描述:**
1. ___________
2. ___________

**建议改进:**
1. ___________
2. ___________
