import React, { useEffect, useState } from 'react'
import { useLocation } from 'react-router-dom'
import { useNavigate } from 'react-router-dom'

function Text() {
  const location = useLocation()
  const searchParams = new URLSearchParams(location.search)
  const navigate = useNavigate()

  const title = searchParams.get('title') || 'Default Title'
  const content = searchParams.get('content') || 'Default Content'
  const bgImage = searchParams.get('bgImage') || ''

  return (
    <div
      className={`min-h-screen bg-gradient-to-b from-green-100 to-green-600 flex flex-col items-center justify-start p-8 relative ${bgImage ? 'bg-cover' : ''}`}
      style={bgImage ? { backgroundImage: `url(${bgImage})`, backgroundSize: 'cover' } : {}}
    >
      {bgImage && <div className="absolute top-0 left-0 w-full h-full"></div>}
      <div className="relative max-w-3xl w-full mt-80 flex flex-col items-center justify-center">
        <h1 className="text-5xl font-bold text-white text-center mb-8">{title}</h1>
        <div className="text-3xl text-white text-left w-full leading-loose indent-10">
          {content}
        </div>
      </div>
      <button
        className="fixed bottom-4 right-4 px-4 py-2 text-black bg-white rounded-lg shadow-lg hover:bg-gray-100"
        onClick={() => navigate('/')}
      >
        返回
      </button>
    </div>
  )
}

export default Text
