import React, { useState, useEffect } from 'react'
import { useToast } from '../contexts/ToastContext'

/**
 * 网络状态组件
 * 监控网络连接状态并在断网时显示友好提示
 */
function NetworkStatus() {
  const [isOnline, setIsOnline] = useState(true)
  const [showOfflineModal, setShowOfflineModal] = useState(false)
  const [isChecking, setIsChecking] = useState(false)
  const { showToast } = useToast()

  useEffect(() => {
    // 获取初始网络状态
    const getInitialStatus = async () => {
      try {
        const status = await window.api.getNetworkStatus()
        setIsOnline(status)
        if (!status) {
          setShowOfflineModal(true)
        }
      } catch (error) {
        console.error('获取网络状态失败:', error)
      }
    }

    getInitialStatus()

    // 监听网络状态变化
    const handleNetworkChange = (online) => {
      const wasOffline = !isOnline
      setIsOnline(online)
      if (online) {
        setShowOfflineModal(false)
        // 如果之前是离线状态，现在恢复了，发送网络恢复事件
        if (wasOffline) {
          window.dispatchEvent(new CustomEvent('network-recovered'))
          showToast('网络连接已恢复', 'success')
        }
      } else {
        setShowOfflineModal(true)
      }
    }

    window.api.onNetworkStatusChanged(handleNetworkChange)

    // 清理监听器
    return () => {
      window.api.removeNetworkStatusListener()
    }
  }, [])

  /**
   * 手动检查网络状态
   */
  const handleRetryConnection = async () => {
    setIsChecking(true)
    try {
      const status = await window.api.checkNetworkStatus()
      setIsOnline(status)
      if (status) {
        setShowOfflineModal(false)
        // 发送网络恢复事件，通知其他组件重新加载数据
        window.dispatchEvent(new CustomEvent('network-recovered'))
        showToast('网络连接已恢复', 'success')
      } else {
        showToast('网络仍然断开，请检查网络设置', 'error')
      }
    } catch (error) {
      console.error('检查网络状态失败:', error)
      showToast('检查网络状态失败', 'error')
    } finally {
      setIsChecking(false)
    }
  }

  // 离线模态框
  const OfflineModal = () =>
    showOfflineModal && (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
          {/* 图标 */}
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <svg
                className="w-8 h-8 text-red-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
          </div>

          {/* 标题 */}
          <h2 className="text-xl font-bold text-gray-800 text-center mb-2">网络连接断开</h2>

          {/* 描述 */}
          <p className="text-gray-600 text-center mb-6">
            检测到网络连接已断开，部分功能可能无法正常使用。请检查您的网络设置后重试。
          </p>

          {/* 建议 */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-gray-800 mb-2">建议操作：</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 检查网络连接是否正常</li>
              <li>• 确认路由器或调制解调器工作正常</li>
              <li>• 检查网络设置配置</li>
              <li>• 联系网络管理员或服务提供商</li>
            </ul>
          </div>

          {/* 按钮 */}
          <div className="flex">
            <button
              onClick={handleRetryConnection}
              disabled={isChecking}
              className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isChecking && (
                <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              )}
              {isChecking ? '检查中...' : '重新检查'}
            </button>
          </div>
        </div>
      </div>
    )

  return (
    <>
      <OfflineModal />
    </>
  )
}

export default NetworkStatus
