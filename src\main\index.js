import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import fs from 'fs'
import { electronApp, is } from '@electron-toolkit/utils'
import schedule from 'node-schedule'
import moment from 'moment'

let mainWindow = null
let appConfig = null
let isOnline = true

function loadAppConfig() {
  try {
    const configPath = is.dev
      ? join(app.getAppPath(), 'resources', 'app-config.json')
      : join(process.resourcesPath, 'app-config.json')
    appConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'))
  } catch (error) {
    console.error('Error reading app configuration:', error)
    appConfig = { title: '中国烟草', menus: [], id: 8, retentionDays: 30, photoPath: 'D:/photos' }
  }
}

function createWindow() {
  // 创建浏览器窗口。
  mainWindow = new BrowserWindow({
    width: 1080,
    height: 1920,
    show: false,
    autoHideMenuBar: true,
    fullscreen: true,
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      contextIsolation: true,
      nodeIntegration: true,
      webSecurity: false,
      allowRunningInsecureContent: true
    }
  })

  // 配置 session
  const ses = mainWindow.webContents.session

  // 清除所有缓存和存储
  ses.clearCache()
  ses.clearStorageData()

  // 配置请求过滤
  ses.webRequest.onBeforeRequest((details, callback) => {
    callback({})
  })

  // 配置响应头
  ses.webRequest.onHeadersReceived((details, callback) => {
    // 移除已存在的响应头
    const responseHeaders = { ...details.responseHeaders }
    Object.keys(responseHeaders).forEach((key) => {
      if (
        key.toLowerCase() === 'access-control-allow-origin' ||
        key.toLowerCase() === 'content-security-policy' ||
        key.toLowerCase() === 'x-frame-options'
      ) {
        delete responseHeaders[key]
      }
    })

    callback({
      responseHeaders: {
        ...responseHeaders,
        'Access-Control-Allow-Origin': ['*'],
        'Access-Control-Allow-Methods': ['*'],
        'Access-Control-Allow-Headers': ['*'],
        'X-Frame-Options': ['ALLOWALL'],
        'Content-Security-Policy': [
          "default-src * 'unsafe-inline' 'unsafe-eval' data: blob: filesystem:; " +
            "script-src * 'unsafe-inline' 'unsafe-eval' blob:; " +
            "style-src * 'unsafe-inline'; " +
            'img-src * data: blob: filesystem:; ' +
            'connect-src *; ' +
            'frame-src *; ' +
            'frame-ancestors *; ' +
            'worker-src * blob:; ' +
            'child-src * blob:; ' +
            "object-src 'none';"
        ]
      }
    })
  })

  // 处理 CORS 预检请求
  ses.webRequest.onBeforeSendHeaders((details, callback) => {
    callback({
      requestHeaders: {
        ...details.requestHeaders,
        Origin: ['https://smart.yukunnengyuan.com']
      }
    })
  })

  // 允许所有权限
  ses.setPermissionRequestHandler((webContents, permission, callback) => {
    callback(true)
  })

  // 设置代理规则（如果需要）
  // ses.setProxy({ proxyRules: 'direct://' })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
    // 启动网络状态监控
    startNetworkMonitoring()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // 添加导航处理
  mainWindow.webContents.on('will-navigate', (event, url) => {
    event.preventDefault()
    shell.openExternal(url)
  })

  // 加载远程URL或本地HTML文件
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }

  // 在窗口加载后发送应用配置给渲染进程
  mainWindow.webContents.on('did-finish-load', () => {
    mainWindow.webContents.send('app-config', appConfig)
  })

  // 添加定时清理任务
  setupCleanupTask()
}

// 添加清理任务函数
function setupCleanupTask() {
  try {
    // 读取配置文件
    const configPath = is.dev
      ? join(__dirname, '../../resources/app-config.json')
      : join(process.resourcesPath, 'app-config.json')

    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'))
    const { retentionDays, photoPath } = config

    // 设置每天 12:30 执行的定时任务
    schedule.scheduleJob('30 12 * * *', async () => {
      try {
        console.log('开始执行照片清理任务...')
        console.log('当前时间:', moment().format('YYYY-MM-DD HH:mm:ss'))
        console.log('清理设置:', { retentionDays, photoPath })

        // 获取截止日期
        const cutoffDate = moment().subtract(retentionDays, 'days')
        console.log('清理截止日期:', cutoffDate.format('YYYY-MM-DD'))

        // 读取照片目录
        const dirs = fs.readdirSync(photoPath)
        console.log('现有文件夹:', dirs)

        // 遍历所有日期文件夹
        for (const dir of dirs) {
          // 检查文件夹名称是否符合格式 (YYYYMMDD)
          if (/^\d{8}$/.test(dir)) {
            const dirDate = moment(dir, 'YYYYMMDD')

            // 如果文件夹日期早于截止日期
            if (dirDate.isBefore(cutoffDate)) {
              const dirPath = join(photoPath, dir)

              // 递归删除文件夹及其内容
              fs.rmSync(dirPath, { recursive: true, force: true })
              console.log(`已删除文件夹: ${dirPath}`)
            }
          }
        }

        console.log('照片清理任务完成')
      } catch (error) {
        console.error('照片清理任务失败:', error)
      }
    })

    console.log('照片清理定时任务已设置')
  } catch (error) {
    console.error('设置清理任务失败:', error)
  }
}

app.whenReady().then(() => {
  // 加载应用配置
  loadAppConfig()

  // 设置应用程序模型ID（仅适用于Windows）
  electronApp.setAppUserModelId('com.electron')

  // 自定义快捷键处理函数
  function handleDeveloperToolShortcuts(window) {
    window.webContents.on('before-input-event', (event, input) => {
      if (input.key === 'F12') {
        window.webContents.toggleDevTools()
      }
    })
  }

  // 在开发和生产环境中都支持通过F12键打开或关闭开发者工具
  app.on('browser-window-created', (_, window) => {
    handleDeveloperToolShortcuts(window)
  })

  createWindow()

  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// 在所有窗口关闭后退出，除非在macOS上
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 处理IPC请求以获取应用配置
ipcMain.handle('get-app-config', () => {
  return appConfig
})

/**
 * 检查网络连接状态（优化版）
 * @returns {Promise<boolean>} 网络是否连接
 */
function checkNetworkStatus() {
  return new Promise((resolve) => {
    const connection = require('net').createConnection({
      port: 80,
      host: 'www.baidu.com',
      timeout: 8000 // 增加超时时间到8秒
    })

    let resolved = false

    connection.on('connect', () => {
      if (!resolved) {
        resolved = true
        console.log('internet connected')
        isOnline = true
        if (mainWindow) {
          mainWindow.webContents.send('network-status-changed', true)
        }
        connection.destroy() // 使用destroy而不是end
        resolve(true)
      }
    })

    connection.on('error', (err) => {
      if (!resolved) {
        resolved = true
        console.log('internet disconnected:', err.message)
        isOnline = false
        if (mainWindow) {
          mainWindow.webContents.send('network-status-changed', false)
        }
        resolve(false)
      }
    })

    connection.on('timeout', () => {
      if (!resolved) {
        resolved = true
        console.log('internet connection timeout')
        isOnline = false
        if (mainWindow) {
          mainWindow.webContents.send('network-status-changed', false)
        }
        connection.destroy()
        resolve(false)
      }
    })
  })
}

/**
 * 定期检查网络状态（优化版）
 */
function startNetworkMonitoring() {
  // 初始检查
  checkNetworkStatus()

  // 增加检测间隔到60秒，减少频繁检测
  setInterval(async () => {
    await checkNetworkStatus()
  }, 10000)
}

// 处理获取网络状态的IPC请求
ipcMain.handle('get-network-status', () => {
  return isOnline
})

// 处理手动检查网络状态的IPC请求
ipcMain.handle('check-network-status', async () => {
  return new Promise((resolve) => {
    const connection = require('net').createConnection({
      port: 80,
      host: 'www.baidu.com',
      timeout: 5000
    })

    connection.on('connect', () => {
      isOnline = true
      connection.end()
      resolve(true)
    })

    connection.on('error', () => {
      isOnline = false
      resolve(false)
    })

    connection.on('timeout', () => {
      isOnline = false
      connection.destroy()
      resolve(false)
    })
  })
})

// 修改保存图片的处理函数
ipcMain.handle('save-image', async (event, imageData) => {
  try {
    if (!imageData || !imageData.data || !imageData.fileName) {
      return {
        success: false,
        error: 'Invalid image data or filename'
      }
    }

    const { data, fileName } = imageData
    const base64Data = data.replace(/^data:image\/\w+;base64,/, '')
    const buffer = Buffer.from(base64Data, 'base64')

    // 从配置文件读取照片保存路径
    if (!appConfig || !appConfig.photoPath) {
      return {
        success: false,
        error: 'Photo path not configured in app settings'
      }
    }

    const photoPath = appConfig.photoPath

    try {
      // 从文件名中提取日期和类型
      console.log('Processing fileName:', fileName)
      const [dateDir, photoInfo] = fileName.split('/')
      console.log('dateDir:', dateDir, 'photoInfo:', photoInfo)

      const photoType = photoInfo.split('_')[1] // 获取 document/idcard-front/idcard-back/portrait
      console.log('photoType:', photoType)

      // 映射类型到中文文件夹名
      const typeToFolder = {
        document: '文档',
        idcard: '证件',
        'idcard-front': '证件', // 身份证正面也保存到证件文件夹
        'idcard-back': '证件', // 身份证反面也保存到证件文件夹
        portrait: '人像'
      }

      const folderName = typeToFolder[photoType]
      console.log('folderName:', folderName)

      if (!folderName) {
        return {
          success: false,
          error: `Unknown photo type: ${photoType}. Available types: ${Object.keys(typeToFolder).join(', ')}`
        }
      }

      // 构建完整的保存路径
      const datePath = join(photoPath, dateDir)
      const typePath = join(datePath, folderName)

      // 确保所有必要的目录都存在
      try {
        // 创建主目录
        if (!fs.existsSync(photoPath)) {
          fs.mkdirSync(photoPath, { recursive: true })
        }
        // 创建日期目录
        if (!fs.existsSync(datePath)) {
          fs.mkdirSync(datePath, { recursive: true })
        }
        // 创建类型目录
        if (!fs.existsSync(typePath)) {
          fs.mkdirSync(typePath, { recursive: true })
        }
      } catch (mkdirError) {
        return {
          success: false,
          error: `Failed to create directories: ${mkdirError.message}`,
          details: mkdirError
        }
      }

      // 构建最终的文件路径 - 保持原有的类型标识
      const finalFileName = `photo_${photoType}_${Date.now()}.jpg`
      const filePath = join(typePath, finalFileName)

      // 保存文件
      try {
        fs.writeFileSync(filePath, buffer)
        console.log('Photo saved successfully:', filePath)
        return {
          success: true,
          path: filePath
        }
      } catch (writeError) {
        return {
          success: false,
          error: `Failed to write file: ${writeError.message}`,
          details: writeError
        }
      }
    } catch (fsError) {
      console.error('File system error:', fsError)
      return {
        success: false,
        error: `File system error: ${fsError.message || 'Unknown file system error'}`,
        details: fsError
      }
    }
  } catch (error) {
    console.error('Error saving image:', error)
    return {
      success: false,
      error: `Failed to save image: ${error.message || 'Unknown error'}`,
      details: error
    }
  }
})
