import React, { useEffect, useState, useRef } from 'react'
import { Document, Page, pdfjs } from 'react-pdf'

pdfjs.GlobalWorkerOptions.workerSrc = './src/assets/pdf.worker.mjs'

// 使用相对路径
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  '../assets/pdf.worker.mjs',
  import.meta.url
).toString()

console.log('PDF Worker Source:', pdfjs.GlobalWorkerOptions.workerSrc)

const PdfViewer = ({ pdfUrl }) => {
  const [numPages, setNumPages] = useState(null)
  const [pageNumber, setPageNumber] = useState(1)
  const containerRef = useRef(null)

  useEffect(() => {
    const handleScroll = () => {
      if (containerRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = containerRef.current
        if (scrollTop + clientHeight >= scrollHeight - 0) {
          setPageNumber(pageNumber + 1)
        }
      }
    }

    const containerElement = containerRef.current
    if (containerElement) {
      containerElement.addEventListener('scroll', handleScroll)
    }

    return () => {
      if (containerElement) {
        containerElement.removeEventListener('scroll', handleScroll)
      }
    }
  }, [pageNumber, numPages])

  useEffect(() => {
    // Reset to first page when PDF changes
    setPageNumber(1)
  }, [pdfUrl])

  if (!pdfUrl) {
    return <div>Loading...</div>
  }

  return (
    <div
      style={{
        display: 'flex', // Use Flexbox for centering
        justifyContent: 'center', // Horizontal centering
        alignItems: 'center' // Vertical centering
      }}
    >
      <div
        ref={containerRef}
        style={{
          maxWidth: '100%', // Ensure it doesn't exceed its parent's width
          maxHeight: 'calc(67vh)', // Fixed max-height, adjust 50px as needed
          overflowY: 'auto' // Enable vertical scrolling
        }}
      >
        <Document
          file={pdfUrl}
          pageLayout="twoColumnLeft"
          onLoadSuccess={({ numPages: pages }) => setNumPages(pages)}
        >
          <div>
            {[...Array(numPages)].map((_, index) => (
              <Page
                width={970}
                key={`page_${index + 1}`}
                pageNumber={index + 1}
                renderTextLayer={false}
                renderAnnotationLayer={false}
                style={{
                  display: pageNumber <= index + 1 ? 'block' : 'none',
                }}
              />
            ))}
          </div>
        </Document>
      </div>
    </div>
  )
}


export default PdfViewer
