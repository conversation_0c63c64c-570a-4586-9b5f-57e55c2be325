# Camera组件自动跳转功能实现文档

## 功能概述

在Camera.jsx组件中实现了拍摄流程的自动跳转功能，提升用户体验，减少手动操作。

## 主要功能

### 1. 自动跳转逻辑
- **拍摄模式顺序**: `document` → `idcard` → `portrait`
- **每次拍摄并保存成功后，系统自动跳转到下一个拍摄模式**
- **身份证特殊处理**: 需要完成正面和反面两步拍摄后才跳转到下一种模式

### 2. 身份证拍摄流程
- **第一步**: 拍摄身份证正面
- **第二步**: 拍摄身份证反面  
- **只有完成这两步后才跳转到人像模式**

### 3. 用户体验优化
- **倒计时显示**: 显示3秒倒计时，让用户知道即将自动跳转
- **可取消跳转**: 用户可以通过手动切换模式来取消自动跳转
- **智能提示**: 不同阶段显示相应的提示信息

## 技术实现

### 新增状态管理
```javascript
const [autoSwitchCountdown, setAutoSwitchCountdown] = useState(0) // 自动切换倒计时
const [nextModeInfo, setNextModeInfo] = useState(null) // 下一个模式信息
```

### 核心函数

#### 1. 拍摄模式顺序定义
```javascript
const shootingModeOrder = ['document', 'idcard', 'portrait']
```

#### 2. 获取下一个模式
```javascript
const getNextMode = (currentMode) => {
  const currentIndex = shootingModeOrder.indexOf(currentMode)
  if (currentIndex < shootingModeOrder.length - 1) {
    return shootingModeOrder[currentIndex + 1]
  }
  return null // 已经是最后一个模式
}
```

#### 3. 模式名称转换
```javascript
const getModeName = (mode) => {
  switch (mode) {
    case 'document': return '文档'
    case 'idcard': return '身份证'
    case 'portrait': return '人像'
    default: return mode
  }
}
```

#### 4. 自动切换倒计时
```javascript
const startAutoSwitch = (targetMode, delay = 3000) => {
  // 设置下一个模式信息和倒计时
  // 3秒后自动切换到目标模式
}
```

#### 5. 取消自动切换
```javascript
const cancelAutoSwitch = () => {
  setAutoSwitchCountdown(0)
  setNextModeInfo(null)
}
```

### 修改的核心逻辑

#### handleSave函数优化
- **身份证正面拍摄完成**: 自动切换到反面，延迟1.5秒
- **身份证反面拍摄完成**: 启动3秒倒计时，自动跳转到人像模式
- **其他模式拍摄完成**: 启动3秒倒计时，自动跳转到下一个模式
- **最后一个模式完成**: 显示"所有拍摄模式已完成"提示

#### 用户界面增强
- **倒计时遮罩**: 显示倒计时数字和目标模式信息
- **手动取消**: 用户点击任何模式切换按钮都会取消自动跳转
- **智能提示**: 根据当前状态显示相应的操作提示

## 使用流程

1. **启动拍摄**: 默认从文档模式开始
2. **文档拍摄**: 拍摄并保存后，3秒倒计时自动跳转到身份证模式
3. **身份证正面**: 拍摄并保存后，1.5秒后自动切换到反面
4. **身份证反面**: 拍摄并保存后，3秒倒计时自动跳转到人像模式
5. **人像拍摄**: 拍摄并保存后，显示完成提示
6. **手动控制**: 任何时候都可以手动切换模式，取消自动跳转

## 特殊处理

### 身份证模式
- 正面拍摄完成后不会跳转到其他模式，而是切换到反面
- 只有正反面都完成后才会跳转到下一个模式（人像）
- 手动切换到身份证模式时会重置为正面

### 最后模式处理
- 当前已经是最后一种拍摄模式（人像）时，不会尝试跳转
- 显示"所有拍摄模式已完成"的提示信息

## 用户体验改进

1. **视觉反馈**: 倒计时数字大而清晰，用户能够明确知道跳转时间
2. **操作灵活**: 用户可以随时手动切换，不会被强制自动跳转
3. **信息明确**: 清楚显示即将跳转到哪个模式
4. **流程顺畅**: 减少了用户的手动操作，提升拍摄效率

## 兼容性

- 保持了原有的手动模式切换功能
- 不影响现有的拍摄和保存逻辑
- 向后兼容，不会破坏现有功能
