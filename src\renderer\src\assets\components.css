@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* 主要按钮 */
  .btn-primary {
    @apply w-full px-3 py-2 bg-gradient-to-r from-teal-500 to-emerald-500
           text-white rounded-lg shadow-lg hover:shadow-xl
           transition-all duration-300 transform hover:scale-105
           flex items-center justify-center gap-2
           whitespace-nowrap overflow-hidden;
  }

  /* 次要按钮 */
  .btn-secondary {
    @apply w-full px-3 py-2 bg-gray-500 hover:bg-gray-600
           text-white rounded-lg shadow-md hover:shadow-lg
           transition-all duration-300 transform hover:scale-105
           flex items-center justify-center gap-2
           whitespace-nowrap overflow-hidden;
  }

  /* 危险按钮 */
  .btn-danger {
    @apply w-full px-3 py-2 bg-gradient-to-r from-red-500 to-rose-500
           text-white rounded-lg shadow-md hover:shadow-lg
           transition-all duration-300 transform hover:scale-105
           flex items-center justify-center gap-2
           whitespace-nowrap overflow-hidden;
  }

  /* 警告按钮 */
  .btn-warning {
    @apply w-full px-3 py-2 bg-gradient-to-r from-amber-400 to-orange-500
           text-white rounded-lg shadow-md hover:shadow-lg
           transition-all duration-300 transform hover:scale-105
           flex items-center justify-center gap-2
           whitespace-nowrap overflow-hidden;
  }

  /* 成功按钮 */
  .btn-success {
    @apply w-full px-3 py-2 bg-gradient-to-r from-green-500 to-emerald-500
           text-white rounded-lg shadow-md hover:shadow-lg
           transition-all duration-300 transform hover:scale-105
           flex items-center justify-center gap-2
           whitespace-nowrap overflow-hidden;
  }

  /* 描边按钮 */
  .btn-outline {
    @apply w-full px-3 py-2 border-2 border-teal-500 text-teal-500
           hover:bg-gradient-to-r hover:from-teal-500 hover:to-emerald-500 hover:text-white
           rounded-lg transition-all duration-300
           transform hover:scale-105
           flex items-center justify-center gap-2
           whitespace-nowrap overflow-hidden;
  }
  .btn-outline.active {
    @apply bg-gradient-to-r from-teal-500 to-emerald-500 text-white;
  }

  /* 链接按钮 */
  .btn-link {
    @apply px-3 py-2 text-teal-500 hover:text-teal-600 underline
           transition-colors duration-300 flex items-center gap-2
           whitespace-nowrap overflow-hidden;
  }

  /* 图标按钮 */
  .btn-icon {
    @apply p-2 rounded-full hover:bg-gray-100
           transition-colors duration-300 flex items-center justify-center
           whitespace-nowrap overflow-hidden;
  }

  /* 禁用状态 - 可以配合其他按钮类使用 */
  .btn-disabled {
    @apply opacity-40 cursor-not-allowed pointer-events-none 
           bg-gray-300 hover:bg-gray-300
           shadow-none hover:shadow-none
           transform-none hover:scale-100;
  }
  .btn-nav {
      @apply w-auto px-6 py-2 bg-gradient-to-r 
             text-white rounded-lg shadow-lg
             transition-all duration-300 transform hover:scale-105
             flex items-center justify-center gap-2
             whitespace-nowrap overflow-hidden
             font-medium;
    }
  
    .btn-nav-purple {
      @apply from-purple-500 to-violet-500 hover:shadow-violet-200;
    }
}