import { contextBridge, shell, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

// Custom APIs for renderer
const api = {
  openExternal: (url) => shell.openExternal(url),
  openLocalFile: shell.openPath,
  getAppConfig: () => ipcRenderer.invoke('get-app-config'),
  saveImage: (options) => ipcRenderer.invoke('save-image', options),
  // 网络状态相关API
  getNetworkStatus: () => ipcRenderer.invoke('get-network-status'),
  checkNetworkStatus: () => ipcRenderer.invoke('check-network-status'),
  onNetworkStatusChanged: (callback) => {
    ipcRenderer.on('network-status-changed', (event, isOnline) => {
      callback(isOnline)
    })
  },
  removeNetworkStatusListener: () => {
    ipcRenderer.removeAllListeners('network-status-changed')
  }
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  window.electron = electronAPI
  window.api = api
}

// 添加必要的权限
window.addEventListener('DOMContentLoaded', () => {
  // 允许跨域请求
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'
})
