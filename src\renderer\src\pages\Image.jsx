import React from 'react';
import { useLocation } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';

function Image() {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const navigate = useNavigate();

  const bgImage = searchParams.get('bgImage') || ''; // 直接获取 bgImage 参数

  return (
    <div 
      className={`min-h-screen bg-gradient-to-b from-green-100 to-green-200 flex flex-col items-center justify-start p-8 relative ${bgImage ? 'bg-cover' : ''}`}
      style={bgImage ? { backgroundImage: `url(${bgImage})`, backgroundSize: 'cover', backgroundPosition: 'center' } : {}}
    >
      {bgImage && <div className="absolute top-0 left-0 w-full h-full "></div>}

      <button 
        className="fixed bottom-4 right-4 px-4 py-2 text-black bg-white rounded-lg shadow-lg hover:bg-gray-100"
        onClick={() => navigate('/')}
      >
        返回
      </button>
    </div>
  );
}

export default Image;