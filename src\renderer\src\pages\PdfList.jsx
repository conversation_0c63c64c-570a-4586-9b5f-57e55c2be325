import React, { useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { IoArrowBack, IoArrowForward, IoHomeOutline } from 'react-icons/io5'
import PdfViewer from './PdfViewer'
import { IoChevronBack, IoChevronForward } from 'react-icons/io5' // 添加新图标

const PdfList = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const searchParams = new URLSearchParams(location.search)
  const title = searchParams.get('title')
  const bgImage = searchParams.get('bgImage')

  const data = JSON.parse(searchParams.get('data')) // 解析 data 参数为对象数组
  const [currentPdfIndex, setCurrentPdfIndex] = useState(0)

  const handlePDFClick = (index) => {
    setCurrentPdfIndex(index)
    setSidebarOpen(false) // 选中后自动收起侧边栏
  }
  const handlePrevClick = () => {
    if (currentPdfIndex > 0) {
      setCurrentPdfIndex(currentPdfIndex - 1)
    }
  }

  const handleNextClick = () => {
    if (currentPdfIndex < data.length - 1) {
      setCurrentPdfIndex(currentPdfIndex + 1)
    }
  }

  const [isSidebarOpen, setSidebarOpen] = useState(false)
  const handleOutsideClick = (e) => {
    // 如果侧边栏是打开的，且点击的不是侧边栏区域，则关闭侧边栏
    if (isSidebarOpen) {
      setSidebarOpen(false)
    }
  }
  return (
    <div
      className={`h-screen relative ${bgImage ? 'bg-cover' : ''}`}
      style={bgImage ? {
        backgroundImage: `url(${bgImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      } : {}}
      onClick={handleOutsideClick} // 添加点击处理
    >
      {/* 左侧边栏 */}
      <div 
        className={`fixed left-0 top-1/2 -translate-y-1/2 z-50 transition-all duration-300 flex ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-[280px]'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="w-[280px] max-h-[80vh] bg-white/80 backdrop-blur-sm shadow-[0_0_15px_rgba(0,0,0,0.1)] 
          rounded-r-2xl p-4 flex flex-col">
          <h2 className="text-lg font-bold text-emerald-800/90 mb-4">文档</h2>
          <div className="flex-1 overflow-y-auto">
            <div className="grid grid-cols-1 gap-2">
              {data.map((item, index) => (
                <div
                  key={`pdf-${index}`}
                  onClick={() => handlePDFClick(index)}
                  className={`
                    cursor-pointer p-3 rounded-lg transition-all duration-300
                    flex items-center gap-3
                    ${currentPdfIndex === index
                      ? 'bg-gradient-to-r from-emerald-600 to-emerald-500 text-white'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                    }
                  `}
                >
                  <div className="w-6 h-6 flex-shrink-0">
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <span className="text-sm font-medium line-clamp-2">
                    {item.name}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
        {/* 展开/收起按钮 */}
        <button
          onClick={() => setSidebarOpen(!isSidebarOpen)}
          className="h-12 w-8 bg-white/80 backdrop-blur-sm shadow-[0_0_15px_rgba(0,0,0,0.1)] 
            flex items-center justify-center self-center rounded-r-xl 
            hover:bg-emerald-50 transition-colors group"
        >
          {isSidebarOpen ? (
            <IoChevronBack className="w-6 h-6 text-emerald-600 group-hover:text-emerald-700" />
          ) : (
            <IoChevronForward className="w-6 h-6 text-emerald-600 group-hover:text-emerald-700" />
          )}
        </button>
      </div>

      {/* 主内容区域 */}
      <div 
        className="h-full flex flex-col" 
        style={{ padding: '263px 63px 35px 34px' }}
      >
        {/* 标题区域 */}
        <div className="bg-white/30 backdrop-blur-sm rounded-2xl px-8 py-6 shadow-lg mb-10">
          <h1 className="text-3xl font-bold text-center text-emerald-900 drop-shadow-[0_2px_2px_rgba(255,255,255,0.5)]">
            {data[currentPdfIndex]?.name || title || 'PDF 查看器'}
          </h1>
          <p className="text-xl text-center mt-2 text-emerald-800 font-medium drop-shadow-[0_2px_2px_rgba(255,255,255,0.5)]">
            第 {currentPdfIndex + 1} / {data.length} 篇
          </p>
        </div>

        {/* PDF查看区域 */}
        <div className="flex-1">
          <PdfViewer pdfUrl={data[currentPdfIndex]?.url} />
        </div>

        {/* 底部按钮区域 */}
        <div>
          <div className="bg-white/95 backdrop-blur-sm rounded-xl shadow-lg px-8 py-4 flex justify-between items-center">
            <div className="flex gap-4">
              <button
                className={`btn-primary w-auto px-6 ${currentPdfIndex === 0 ? 'btn-disabled' : ''}`}
                onClick={handlePrevClick}
                disabled={currentPdfIndex === 0}
              >
                <IoArrowBack className="text-xl" />
                上一篇
              </button>
              <button
                className={`btn-primary w-auto px-6 ${currentPdfIndex === data.length - 1 ? 'btn-disabled' : ''}`}
                onClick={handleNextClick}
                disabled={currentPdfIndex === data.length - 1}
              >
                下一篇
                <IoArrowForward className="text-xl" />
              </button>
            </div>
            <button
              className="btn-nav btn-nav-purple"
              onClick={() => navigate('/')}
            >
              <IoHomeOutline className="text-xl" />
              首页
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PdfList
