import './assets/main.css'
import './styles/index.css'
import React from 'react'
import ReactDOM from 'react-dom/client'
import { createHashRouter, RouterProvider } from 'react-router-dom'
import Home from './pages/Home'
import Image from './pages/Image'
import Text from './pages/Text'
import PdfViewer from './pages/PdfViewer'
import PdfList from './pages/PdfList'
import { ToastProvider } from './contexts/ToastContext'
import { NetworkProvider } from './contexts/NetworkContext'
import NetworkStatus from './components/NetworkStatus'

//createBrowserRouter
const router = createHashRouter([
  {
    path: '/',
    element: <Home />
  },
  {
    path: '/image',
    element: <Image />
  },
  {
    path: '/text',
    element: <Text />
  },
  {
    path: '/PdfList',
    element: <PdfList />
  },
  {
    path: '/pdf',
    element: <PdfViewer />
  }
])

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <NetworkProvider>
      <ToastProvider>
        <RouterProvider router={router} />
        <NetworkStatus />
      </ToastProvider>
    </NetworkProvider>
  </React.StrictMode>
)
