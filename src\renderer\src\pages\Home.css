.home-container {
    position: relative;
    min-height: 100vh;
    padding: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: start;
  }
  
  .home-bg-img {
    width: 100%;
    height: auto;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
  }
  
  .menu-buttons {
    position: relative;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    width: 100%;
    max-width: 400px;
    margin-top: 80px;
    z-index: 10;
  }